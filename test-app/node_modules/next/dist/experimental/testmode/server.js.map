{"version": 3, "sources": ["../../../src/experimental/testmode/server.ts"], "sourcesContent": ["import type { WorkerRe<PERSON>Handler } from '../../server/lib/types'\nimport type { NodeRequestHandler } from '../../server/next-server'\nimport { withRequest, type TestRequestReader } from './context'\nimport { interceptFetch } from './fetch'\nimport { interceptHttpGet } from './httpget'\nimport type { IncomingMessage } from 'http'\n\nconst reader: TestRequestReader<IncomingMessage> = {\n  url(req) {\n    return req.url ?? ''\n  },\n  header(req, name) {\n    const h = req.headers[name]\n    if (h === undefined || h === null) {\n      return null\n    }\n    if (typeof h === 'string') {\n      return h\n    }\n    return h[0] ?? null\n  },\n}\n\nexport function interceptTestApis(): () => void {\n  const originalFetch = global.fetch\n  const restoreFetch = interceptFetch(originalFetch)\n  const restoreHttpGet = interceptHttpGet(originalFetch)\n\n  // Cleanup.\n  return () => {\n    restoreFetch()\n    restoreHttpGet()\n  }\n}\n\nexport function wrapRequestHandlerWorker(\n  handler: WorkerRequestHandler\n): WorkerRequestHandler {\n  return (req, res) => withRequest(req, reader, () => handler(req, res))\n}\n\nexport function wrapRequestHandlerNode(\n  handler: NodeRequestHandler\n): NodeRequestHandler {\n  return (req, res, parsedUrl) =>\n    withRequest(req, reader, () => handler(req, res, parsedUrl))\n}\n"], "names": ["interceptTestApis", "wrapRequestHandlerNode", "wrapRequestHandlerWorker", "reader", "url", "req", "header", "name", "h", "headers", "undefined", "originalFetch", "global", "fetch", "restoreFetch", "interceptFetch", "restoreHttpGet", "interceptHttpGet", "handler", "res", "withRequest", "parsedUrl"], "mappings": ";;;;;;;;;;;;;;;;IAuBgBA,iBAAiB;eAAjBA;;IAkBAC,sBAAsB;eAAtBA;;IANAC,wBAAwB;eAAxBA;;;yBAjCoC;uBACrB;yBACE;AAGjC,MAAMC,SAA6C;IACjDC,KAAIC,GAAG;QACL,OAAOA,IAAID,GAAG,IAAI;IACpB;IACAE,QAAOD,GAAG,EAAEE,IAAI;QACd,MAAMC,IAAIH,IAAII,OAAO,CAACF,KAAK;QAC3B,IAAIC,MAAME,aAAaF,MAAM,MAAM;YACjC,OAAO;QACT;QACA,IAAI,OAAOA,MAAM,UAAU;YACzB,OAAOA;QACT;QACA,OAAOA,CAAC,CAAC,EAAE,IAAI;IACjB;AACF;AAEO,SAASR;IACd,MAAMW,gBAAgBC,OAAOC,KAAK;IAClC,MAAMC,eAAeC,IAAAA,qBAAc,EAACJ;IACpC,MAAMK,iBAAiBC,IAAAA,yBAAgB,EAACN;IAExC,WAAW;IACX,OAAO;QACLG;QACAE;IACF;AACF;AAEO,SAASd,yBACdgB,OAA6B;IAE7B,OAAO,CAACb,KAAKc,MAAQC,IAAAA,oBAAW,EAACf,KAAKF,QAAQ,IAAMe,QAAQb,KAAKc;AACnE;AAEO,SAASlB,uBACdiB,OAA2B;IAE3B,OAAO,CAACb,KAAKc,KAAKE,YAChBD,IAAAA,oBAAW,EAACf,KAAKF,QAAQ,IAAMe,QAAQb,KAAKc,KAAKE;AACrD"}