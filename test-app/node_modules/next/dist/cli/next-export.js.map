{"version": 3, "sources": ["../../src/cli/next-export.ts"], "sourcesContent": ["import { cyan } from '../lib/picocolors'\nimport { error } from '../build/output/log'\n\nconst nextExport = () => {\n  error(`\n    \\`next export\\` has been removed in favor of 'output: export' in next.config.js.\\nLearn more: ${cyan(\n      'https://nextjs.org/docs/app/building-your-application/deploying/static-exports'\n    )}\n  `)\n\n  process.exit(1)\n}\n\nexport { nextExport }\n"], "names": ["nextExport", "error", "cyan", "process", "exit"], "mappings": ";;;;+BAaSA;;;eAAAA;;;4BAbY;qBACC;AAEtB,MAAMA,aAAa;IACjBC,IAAAA,UAAK,EAAC,CAAC;kGACyF,EAAEC,IAAAA,gBAAI,EAClG,kFACA;EACJ,CAAC;IAEDC,QAAQC,IAAI,CAAC;AACf"}