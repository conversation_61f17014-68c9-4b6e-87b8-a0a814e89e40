'use client';

import { useState, useEffect } from 'react';
import { mockBedrockService } from '@/lib/bedrock';
import { 
  CogIcon, 
  PlayIcon, 
  PauseIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

interface MockConfigPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function MockConfigPanel({ isOpen, onClose }: MockConfigPanelProps) {
  const [mockDelay, setMockDelay] = useState(2000);
  const [isTestingOCR, setIsTestingOCR] = useState(false);
  const [isTestingStructured, setIsTestingStructured] = useState(false);
  const [isTestingFormDetection, setIsTestingFormDetection] = useState(false);
  const [testResults, setTestResults] = useState<{
    ocr?: { success: boolean; time: number };
    structured?: { success: boolean; time: number };
    formDetection?: { success: boolean; time: number };
  }>({});

  useEffect(() => {
    setMockDelay(mockBedrockService.getMockDelay());
  }, []);

  const handleDelayChange = (newDelay: number) => {
    setMockDelay(newDelay);
    mockBedrockService.setMockDelay(newDelay);
  };

  const testOCRExtraction = async () => {
    setIsTestingOCR(true);
    const startTime = Date.now();
    
    try {
      const result = await mockBedrockService.extractTextFromDocument();
      const endTime = Date.now();
      
      setTestResults(prev => ({
        ...prev,
        ocr: { success: result.success, time: endTime - startTime }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        ocr: { success: false, time: Date.now() - startTime }
      }));
    } finally {
      setIsTestingOCR(false);
    }
  };

  const testStructuredExtraction = async () => {
    setIsTestingStructured(true);
    const startTime = Date.now();
    
    try {
      const result = await mockBedrockService.extractStructuredData();
      const endTime = Date.now();
      
      setTestResults(prev => ({
        ...prev,
        structured: { success: result.success, time: endTime - startTime }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        structured: { success: false, time: Date.now() - startTime }
      }));
    } finally {
      setIsTestingStructured(false);
    }
  };

  const testFormDetection = async () => {
    setIsTestingFormDetection(true);
    const startTime = Date.now();
    
    try {
      const result = await mockBedrockService.detectFormFields();
      const endTime = Date.now();
      
      setTestResults(prev => ({
        ...prev,
        formDetection: { success: result.success, time: endTime - startTime }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        formDetection: { success: false, time: Date.now() - startTime }
      }));
    } finally {
      setIsTestingFormDetection(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <CogIcon className="h-6 w-6 text-gray-600 mr-2" />
              <h2 className="text-xl font-semibold text-gray-900">Mock Service Configuration</h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XCircleIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4 space-y-6">
          {/* Processing Delay Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Processing Simulation</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Processing Delay (milliseconds)
              </label>
              <div className="flex items-center space-x-4">
                <input
                  type="range"
                  min="0"
                  max="10000"
                  step="500"
                  value={mockDelay}
                  onChange={(e) => handleDelayChange(Number(e.target.value))}
                  className="flex-1"
                />
                <div className="flex items-center space-x-2">
                  <ClockIcon className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-900 min-w-[4rem]">
                    {mockDelay}ms
                  </span>
                </div>
              </div>
              <div className="mt-2 flex justify-between text-xs text-gray-500">
                <span>Instant</span>
                <span>10 seconds</span>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Simulates the processing time for AWS Bedrock operations. Set to 0 for instant responses.
              </p>
            </div>
          </div>

          {/* Service Testing */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Service Testing</h3>
            
            {/* OCR Testing */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">Text Extraction (OCR)</h4>
                <button
                  onClick={testOCRExtraction}
                  disabled={isTestingOCR}
                  className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isTestingOCR ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Testing...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4 mr-1" />
                      Test
                    </>
                  )}
                </button>
              </div>
              {testResults.ocr && (
                <div className="flex items-center space-x-2 text-sm">
                  {testResults.ocr.success ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircleIcon className="h-4 w-4 text-red-500" />
                  )}
                  <span className={testResults.ocr.success ? 'text-green-700' : 'text-red-700'}>
                    {testResults.ocr.success ? 'Success' : 'Failed'}
                  </span>
                  <span className="text-gray-500">
                    ({testResults.ocr.time}ms)
                  </span>
                </div>
              )}
            </div>

            {/* Structured Data Testing */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">Structured Data Extraction</h4>
                <button
                  onClick={testStructuredExtraction}
                  disabled={isTestingStructured}
                  className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isTestingStructured ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Testing...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4 mr-1" />
                      Test
                    </>
                  )}
                </button>
              </div>
              {testResults.structured && (
                <div className="flex items-center space-x-2 text-sm">
                  {testResults.structured.success ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircleIcon className="h-4 w-4 text-red-500" />
                  )}
                  <span className={testResults.structured.success ? 'text-green-700' : 'text-red-700'}>
                    {testResults.structured.success ? 'Success' : 'Failed'}
                  </span>
                  <span className="text-gray-500">
                    ({testResults.structured.time}ms)
                  </span>
                </div>
              )}
            </div>

            {/* Form Detection Testing */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">Form Field Detection</h4>
                <button
                  onClick={testFormDetection}
                  disabled={isTestingFormDetection}
                  className="px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isTestingFormDetection ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Testing...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4 mr-1" />
                      Test
                    </>
                  )}
                </button>
              </div>
              {testResults.formDetection && (
                <div className="flex items-center space-x-2 text-sm">
                  {testResults.formDetection.success ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircleIcon className="h-4 w-4 text-red-500" />
                  )}
                  <span className={testResults.formDetection.success ? 'text-green-700' : 'text-red-700'}>
                    {testResults.formDetection.success ? 'Success' : 'Failed'}
                  </span>
                  <span className="text-gray-500">
                    ({testResults.formDetection.time}ms)
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Mock Data Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Mock Data Information</h3>
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Available Mock Document Types</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• <strong>Auto Insurance:</strong> Triggered by filenames containing "auto", "car", or "vehicle"</li>
                <li>• <strong>Home Insurance:</strong> Triggered by filenames containing "home", "house", or "property"</li>
                <li>• <strong>Life Insurance:</strong> Triggered by filenames containing "life" or "term"</li>
                <li>• <strong>Random:</strong> Random document type for other filenames</li>
              </ul>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-2">Mock Features</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Realistic insurance document text generation</li>
                <li>• Structured data extraction with confidence scores</li>
                <li>• Form field detection with bounding boxes</li>
                <li>• Configurable processing delays</li>
                <li>• Random but consistent data generation</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
