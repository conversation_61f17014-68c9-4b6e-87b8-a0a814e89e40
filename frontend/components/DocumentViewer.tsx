'use client';

import { useState, useEffect } from 'react';
import { Document as DocumentType } from '@/types';
import { supabase, getFileUrl } from '@/lib/supabase';
import { 
  DocumentTextIcon, 
  EyeIcon, 
  PencilIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon
} from '@heroicons/react/24/outline';

interface DocumentViewerProps {
  document: DocumentType;
  extractedText?: string;
  onTextEdit?: (text: string) => void;
  showExtractedText?: boolean;
  onToggleView?: () => void;
}

export default function DocumentViewer({
  document,
  extractedText,
  onTextEdit,
  showExtractedText = false,
  onToggleView
}: DocumentViewerProps) {
  const [documentUrl, setDocumentUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [isEditingText, setIsEditingText] = useState(false);
  const [editableText, setEditableText] = useState(extractedText || '');
  const [zoom, setZoom] = useState(100);

  useEffect(() => {
    loadDocument();
  }, [document]);

  useEffect(() => {
    setEditableText(extractedText || '');
  }, [extractedText]);

  const loadDocument = async () => {
    try {
      setIsLoading(true);
      setError('');

      // Get the public URL for the document
      const url = getFileUrl('documents', document.file_path);
      setDocumentUrl(url);
    } catch (err) {
      console.error('Error loading document:', err);
      setError('Failed to load document');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async () => {
    try {
      const { data, error } = await supabase.storage
        .from('documents')
        .download(document.file_path);

      if (error) throw error;

      // Create download link
      const url = URL.createObjectURL(data);
      const a = document.createElement('a');
      a.href = url;
      a.download = document.original_filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading document:', err);
    }
  };

  const handleSaveText = () => {
    if (onTextEdit) {
      onTextEdit(editableText);
    }
    setIsEditingText(false);
  };

  const handleCancelEdit = () => {
    setEditableText(extractedText || '');
    setIsEditingText(false);
  };

  const adjustZoom = (delta: number) => {
    setZoom(prev => Math.max(25, Math.min(200, prev + delta)));
  };

  const isPDF = document.content_type === 'application/pdf';
  const isImage = document.content_type.startsWith('image/');

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading document...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-red-50 rounded-lg">
        <div className="text-center">
          <DocumentTextIcon className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
          <button
            onClick={loadDocument}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              {document.original_filename}
            </h3>
            <p className="text-sm text-gray-500">
              {document.content_type} • {(document.file_size / 1024 / 1024).toFixed(2)} MB
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {/* View Toggle */}
            {extractedText && onToggleView && (
              <button
                onClick={onToggleView}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  showExtractedText
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <EyeIcon className="h-4 w-4 inline mr-1" />
                {showExtractedText ? 'Show Original' : 'Show Extracted Text'}
              </button>
            )}
            
            {/* Zoom Controls (for original document view) */}
            {!showExtractedText && (
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => adjustZoom(-25)}
                  className="p-2 text-gray-600 hover:text-gray-900"
                  title="Zoom Out"
                >
                  <MagnifyingGlassMinusIcon className="h-4 w-4" />
                </button>
                <span className="text-sm text-gray-600 min-w-[3rem] text-center">
                  {zoom}%
                </span>
                <button
                  onClick={() => adjustZoom(25)}
                  className="p-2 text-gray-600 hover:text-gray-900"
                  title="Zoom In"
                >
                  <MagnifyingGlassPlusIcon className="h-4 w-4" />
                </button>
              </div>
            )}

            {/* Download Button */}
            <button
              onClick={handleDownload}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Download"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {showExtractedText ? (
          /* Extracted Text View */
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-md font-medium text-gray-900">Extracted Text</h4>
              {onTextEdit && (
                <button
                  onClick={() => setIsEditingText(!isEditingText)}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <PencilIcon className="h-4 w-4 inline mr-1" />
                  {isEditingText ? 'Cancel' : 'Edit'}
                </button>
              )}
            </div>
            
            {isEditingText ? (
              <div className="space-y-4">
                <textarea
                  value={editableText}
                  onChange={(e) => setEditableText(e.target.value)}
                  className="w-full h-96 p-4 border border-gray-300 rounded-md font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter extracted text..."
                />
                <div className="flex space-x-2">
                  <button
                    onClick={handleSaveText}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Save Changes
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 rounded-md p-4 max-h-96 overflow-y-auto">
                <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                  {extractedText || 'No extracted text available.'}
                </pre>
              </div>
            )}
          </div>
        ) : (
          /* Original Document View */
          <div className="text-center">
            {isPDF ? (
              <div className="bg-gray-100 rounded-lg p-8">
                <DocumentTextIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">PDF Preview</p>
                <p className="text-sm text-gray-500">
                  PDF viewing requires a browser plugin or download to view the full document.
                </p>
                <button
                  onClick={handleDownload}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Download PDF
                </button>
              </div>
            ) : isImage ? (
              <div className="max-w-full overflow-auto">
                <img
                  src={documentUrl}
                  alt={document.original_filename}
                  className="max-w-none mx-auto rounded-lg shadow-md"
                  style={{ transform: `scale(${zoom / 100})` }}
                  onError={() => setError('Failed to load image')}
                />
              </div>
            ) : (
              <div className="bg-gray-100 rounded-lg p-8">
                <DocumentTextIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">Document Preview Not Available</p>
                <p className="text-sm text-gray-500">
                  This file type cannot be previewed in the browser.
                </p>
                <button
                  onClick={handleDownload}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Download File
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
