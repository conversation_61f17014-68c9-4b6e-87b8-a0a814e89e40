'use client';

import { useState, useRef, useEffect } from 'react';
import { 
  PlusIcon, 
  TrashIcon, 
  PencilIcon, 
  CheckIcon,
  XMarkIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';

interface DetectedField {
  id: string;
  type: string;
  label: string;
  name: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  required: boolean;
  confidence: number;
  options?: string[];
}

interface FormFieldDetectorProps {
  imageUrl: string;
  detectedFields: DetectedField[];
  onFieldsChange: (fields: DetectedField[]) => void;
  onSaveTemplate?: (fields: DetectedField[]) => void;
  isEditable?: boolean;
}

export default function FormFieldDetector({
  imageUrl,
  detectedFields,
  onFieldsChange,
  onSaveTemplate,
  isEditable = true
}: FormFieldDetectorProps) {
  const [fields, setFields] = useState<DetectedField[]>(detectedFields);
  const [selectedField, setSelectedField] = useState<string | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [showBoundingBoxes, setShowBoundingBoxes] = useState(true);
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawStart, setDrawStart] = useState<{ x: number; y: number } | null>(null);
  const [currentDraw, setCurrentDraw] = useState<{ x: number; y: number; width: number; height: number } | null>(null);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    setFields(detectedFields);
  }, [detectedFields]);

  useEffect(() => {
    onFieldsChange(fields);
  }, [fields, onFieldsChange]);

  const getImageCoordinates = (clientX: number, clientY: number) => {
    if (!imageRef.current || !containerRef.current) return { x: 0, y: 0 };
    
    const imageRect = imageRef.current.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();
    
    const x = clientX - imageRect.left;
    const y = clientY - imageRect.top;
    
    // Convert to image coordinates (accounting for image scaling)
    const scaleX = imageRef.current.naturalWidth / imageRect.width;
    const scaleY = imageRef.current.naturalHeight / imageRect.height;
    
    return {
      x: x * scaleX,
      y: y * scaleY
    };
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!isEditable || editingField) return;
    
    const coords = getImageCoordinates(e.clientX, e.clientY);
    setIsDrawing(true);
    setDrawStart(coords);
    setCurrentDraw({ x: coords.x, y: coords.y, width: 0, height: 0 });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDrawing || !drawStart) return;
    
    const coords = getImageCoordinates(e.clientX, e.clientY);
    setCurrentDraw({
      x: Math.min(drawStart.x, coords.x),
      y: Math.min(drawStart.y, coords.y),
      width: Math.abs(coords.x - drawStart.x),
      height: Math.abs(coords.y - drawStart.y)
    });
  };

  const handleMouseUp = () => {
    if (!isDrawing || !currentDraw || currentDraw.width < 10 || currentDraw.height < 10) {
      setIsDrawing(false);
      setDrawStart(null);
      setCurrentDraw(null);
      return;
    }

    // Create new field
    const newField: DetectedField = {
      id: `field_${Date.now()}`,
      type: 'text',
      label: 'New Field',
      name: 'new_field',
      position: currentDraw,
      required: false,
      confidence: 1.0
    };

    setFields(prev => [...prev, newField]);
    setSelectedField(newField.id);
    setEditingField(newField.id);
    
    setIsDrawing(false);
    setDrawStart(null);
    setCurrentDraw(null);
  };

  const updateField = (fieldId: string, updates: Partial<DetectedField>) => {
    setFields(prev => prev.map(field => 
      field.id === fieldId ? { ...field, ...updates } : field
    ));
  };

  const deleteField = (fieldId: string) => {
    setFields(prev => prev.filter(field => field.id !== fieldId));
    if (selectedField === fieldId) setSelectedField(null);
    if (editingField === fieldId) setEditingField(null);
  };

  const getFieldTypeColor = (type: string) => {
    switch (type) {
      case 'text': return 'border-blue-500 bg-blue-100';
      case 'checkbox': return 'border-green-500 bg-green-100';
      case 'select': return 'border-purple-500 bg-purple-100';
      case 'date': return 'border-orange-500 bg-orange-100';
      default: return 'border-gray-500 bg-gray-100';
    }
  };

  const renderBoundingBox = (field: DetectedField) => {
    if (!imageRef.current) return null;
    
    const imageRect = imageRef.current.getBoundingClientRect();
    const scaleX = imageRect.width / imageRef.current.naturalWidth;
    const scaleY = imageRect.height / imageRef.current.naturalHeight;
    
    const style = {
      position: 'absolute' as const,
      left: field.position.x * scaleX,
      top: field.position.y * scaleY,
      width: field.position.width * scaleX,
      height: field.position.height * scaleY,
      border: '2px solid',
      pointerEvents: 'auto' as const,
      cursor: 'pointer'
    };

    return (
      <div
        key={field.id}
        className={`${getFieldTypeColor(field.type)} ${
          selectedField === field.id ? 'ring-2 ring-offset-2 ring-blue-500' : ''
        }`}
        style={style}
        onClick={(e) => {
          e.stopPropagation();
          setSelectedField(field.id);
        }}
        title={`${field.label} (${field.type})`}
      >
        <div className="absolute -top-6 left-0 bg-black bg-opacity-75 text-white text-xs px-1 rounded">
          {field.label}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-medium text-gray-900">Form Field Detection</h3>
          <button
            onClick={() => setShowBoundingBoxes(!showBoundingBoxes)}
            className={`px-3 py-1 rounded-md text-sm ${
              showBoundingBoxes 
                ? 'bg-blue-100 text-blue-700' 
                : 'bg-gray-100 text-gray-700'
            }`}
          >
            {showBoundingBoxes ? (
              <>
                <EyeIcon className="h-4 w-4 inline mr-1" />
                Hide Boxes
              </>
            ) : (
              <>
                <EyeSlashIcon className="h-4 w-4 inline mr-1" />
                Show Boxes
              </>
            )}
          </button>
        </div>
        
        {onSaveTemplate && (
          <button
            onClick={() => onSaveTemplate(fields)}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Save as Template
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Image with Bounding Boxes */}
        <div className="lg:col-span-2">
          <div 
            ref={containerRef}
            className="relative border border-gray-300 rounded-lg overflow-hidden bg-gray-50"
            style={{ cursor: isDrawing ? 'crosshair' : 'default' }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          >
            <img
              ref={imageRef}
              src={imageUrl}
              alt="Form to analyze"
              className="max-w-full h-auto"
              draggable={false}
            />
            
            {/* Bounding Boxes */}
            {showBoundingBoxes && fields.map(renderBoundingBox)}
            
            {/* Current Drawing */}
            {currentDraw && isDrawing && (
              <div
                className="absolute border-2 border-dashed border-red-500 bg-red-100 bg-opacity-50"
                style={{
                  left: currentDraw.x * (imageRef.current?.getBoundingClientRect().width || 1) / (imageRef.current?.naturalWidth || 1),
                  top: currentDraw.y * (imageRef.current?.getBoundingClientRect().height || 1) / (imageRef.current?.naturalHeight || 1),
                  width: currentDraw.width * (imageRef.current?.getBoundingClientRect().width || 1) / (imageRef.current?.naturalWidth || 1),
                  height: currentDraw.height * (imageRef.current?.getBoundingClientRect().height || 1) / (imageRef.current?.naturalHeight || 1)
                }}
              />
            )}
          </div>
          
          {isEditable && (
            <p className="text-sm text-gray-500 mt-2">
              Click and drag to create new form fields. Click existing fields to select and edit them.
            </p>
          )}
        </div>

        {/* Field List */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Detected Fields ({fields.length})</h4>
          
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {fields.map((field) => (
              <FieldEditor
                key={field.id}
                field={field}
                isSelected={selectedField === field.id}
                isEditing={editingField === field.id}
                onSelect={() => setSelectedField(field.id)}
                onEdit={() => setEditingField(field.id)}
                onSave={(updates) => {
                  updateField(field.id, updates);
                  setEditingField(null);
                }}
                onCancel={() => setEditingField(null)}
                onDelete={() => deleteField(field.id)}
                isEditable={isEditable}
              />
            ))}
          </div>
          
          {fields.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <PlusIcon className="h-8 w-8 mx-auto mb-2" />
              <p>No fields detected</p>
              <p className="text-sm">Draw on the image to create fields</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

interface FieldEditorProps {
  field: DetectedField;
  isSelected: boolean;
  isEditing: boolean;
  onSelect: () => void;
  onEdit: () => void;
  onSave: (updates: Partial<DetectedField>) => void;
  onCancel: () => void;
  onDelete: () => void;
  isEditable: boolean;
}

function FieldEditor({
  field,
  isSelected,
  isEditing,
  onSelect,
  onEdit,
  onSave,
  onCancel,
  onDelete,
  isEditable
}: FieldEditorProps) {
  const [editData, setEditData] = useState(field);

  useEffect(() => {
    setEditData(field);
  }, [field]);

  const handleSave = () => {
    onSave(editData);
  };

  if (isEditing && isEditable) {
    return (
      <div className="border border-blue-300 rounded-lg p-3 bg-blue-50">
        <div className="space-y-3">
          <input
            type="text"
            value={editData.label}
            onChange={(e) => setEditData(prev => ({ ...prev, label: e.target.value }))}
            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
            placeholder="Field Label"
          />
          
          <input
            type="text"
            value={editData.name}
            onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
            placeholder="Field Name"
          />
          
          <select
            value={editData.type}
            onChange={(e) => setEditData(prev => ({ ...prev, type: e.target.value }))}
            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
          >
            <option value="text">Text</option>
            <option value="checkbox">Checkbox</option>
            <option value="select">Select</option>
            <option value="date">Date</option>
            <option value="number">Number</option>
            <option value="email">Email</option>
            <option value="phone">Phone</option>
          </select>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={editData.required}
              onChange={(e) => setEditData(prev => ({ ...prev, required: e.target.checked }))}
              className="mr-2"
            />
            <span className="text-sm">Required</span>
          </label>
          
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              className="flex-1 px-2 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              <CheckIcon className="h-3 w-3 inline mr-1" />
              Save
            </button>
            <button
              onClick={onCancel}
              className="flex-1 px-2 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
            >
              <XMarkIcon className="h-3 w-3 inline mr-1" />
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`border rounded-lg p-3 cursor-pointer transition-colors ${
        isSelected 
          ? 'border-blue-500 bg-blue-50' 
          : 'border-gray-200 bg-white hover:bg-gray-50'
      }`}
      onClick={onSelect}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h5 className="font-medium text-sm text-gray-900 truncate">
            {field.label}
          </h5>
          <p className="text-xs text-gray-500">
            {field.type} • {field.required ? 'Required' : 'Optional'}
          </p>
          <p className="text-xs text-gray-400">
            Confidence: {(field.confidence * 100).toFixed(0)}%
          </p>
        </div>
        
        {isEditable && (
          <div className="flex space-x-1 ml-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onEdit();
              }}
              className="p-1 text-gray-400 hover:text-blue-600"
            >
              <PencilIcon className="h-3 w-3" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="p-1 text-gray-400 hover:text-red-600"
            >
              <TrashIcon className="h-3 w-3" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
