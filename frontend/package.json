{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.490.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@supabase/supabase-js": "^2.39.0", "axios": "^1.9.0", "formidable": "^3.5.1", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-pdf": "^9.2.1", "uuid": "^9.0.1", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/formidable": "^3.4.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^9.0.7", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}