'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { formsAPI } from '@/lib/api';
import { Form, FormField, FieldType } from '@/types';
import {
  ArrowLeftIcon,
  PlusIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

export default function FormEditPage() {
  const params = useParams();
  const router = useRouter();
  const formId = params.id as string;

  const [form, setForm] = useState<Form | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [showAddField, setShowAddField] = useState(false);
  const [newField, setNewField] = useState({
    name: '',
    label: '',
    field_type: FieldType.TEXT,
    required: false,
    placeholder: '',
  });

  useEffect(() => {
    if (formId) {
      fetchForm();
    }
  }, [formId]);

  const fetchForm = async () => {
    try {
      const data = await formsAPI.getById(formId);
      setForm(data);
    } catch (error) {
      console.error('Failed to fetch form:', error);
      router.push('/forms');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateField = async (fieldId: string, updates: Partial<FormField>) => {
    setIsSaving(true);
    try {
      await formsAPI.updateField(fieldId, updates);
      await fetchForm();
      setEditingField(null);
    } catch (error) {
      console.error('Failed to update field:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteField = async (fieldId: string) => {
    if (confirm('Are you sure you want to delete this field?')) {
      try {
        await formsAPI.deleteField(fieldId);
        await fetchForm();
      } catch (error) {
        console.error('Failed to delete field:', error);
      }
    }
  };

  const handleAddField = async () => {
    if (!form || !newField.name || !newField.label) return;

    setIsSaving(true);
    try {
      await formsAPI.addField(form.id, {
        form_id: form.id,
        ...newField,
      });
      await fetchForm();
      setShowAddField(false);
      setNewField({
        name: '',
        label: '',
        field_type: FieldType.TEXT,
        required: false,
        placeholder: '',
      });
    } catch (error) {
      console.error('Failed to add field:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const fieldTypes = Object.values(FieldType);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!form) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">Form not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The form you're looking for doesn't exist.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.push(`/forms/${form.id}`)}
            className="flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Form
          </button>
          <h1 className="text-2xl font-semibold text-gray-900">
            Edit {form.name}
          </h1>
        </div>
        <button
          onClick={() => setShowAddField(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Field
        </button>
      </div>

      {/* Form Fields */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Form Fields
          </h3>
          
          <div className="space-y-4">
            {form.fields.map((field) => (
              <div key={field.id} className="border border-gray-200 rounded-lg p-4">
                {editingField === field.id ? (
                  <EditFieldForm
                    field={field}
                    onSave={(updates) => handleUpdateField(field.id, updates)}
                    onCancel={() => setEditingField(null)}
                    isSaving={isSaving}
                  />
                ) : (
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">
                            {field.label}
                          </h4>
                          <p className="text-sm text-gray-500">
                            {field.name} • {field.field_type}
                            {field.required && ' • Required'}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setEditingField(field.id)}
                        className="text-blue-600 hover:text-blue-900 text-sm"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDeleteField(field.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {form.fields.length === 0 && (
            <div className="text-center py-8">
              <p className="text-sm text-gray-500">
                No fields detected. Add fields manually or run field detection.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Add Field Modal */}
      {showAddField && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Field</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Field Name
                  </label>
                  <input
                    type="text"
                    value={newField.name}
                    onChange={(e) => setNewField({ ...newField, name: e.target.value })}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="field_name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Label
                  </label>
                  <input
                    type="text"
                    value={newField.label}
                    onChange={(e) => setNewField({ ...newField, label: e.target.value })}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Field Label"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Type
                  </label>
                  <select
                    value={newField.field_type}
                    onChange={(e) => setNewField({ ...newField, field_type: e.target.value as FieldType })}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    {fieldTypes.map((type) => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newField.required}
                    onChange={(e) => setNewField({ ...newField, required: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Required field
                  </label>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowAddField(false);
                    setNewField({
                      name: '',
                      label: '',
                      field_type: FieldType.TEXT,
                      required: false,
                      placeholder: '',
                    });
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddField}
                  disabled={!newField.name || !newField.label || isSaving}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {isSaving ? 'Adding...' : 'Add Field'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Component for editing individual fields
function EditFieldForm({
  field,
  onSave,
  onCancel,
  isSaving,
}: {
  field: FormField;
  onSave: (updates: Partial<FormField>) => void;
  onCancel: () => void;
  isSaving: boolean;
}) {
  const [editData, setEditData] = useState({
    name: field.name,
    label: field.label,
    field_type: field.field_type,
    required: field.required,
    placeholder: field.placeholder || '',
  });

  const fieldTypes = Object.values(FieldType);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Field Name
          </label>
          <input
            type="text"
            value={editData.name}
            onChange={(e) => setEditData({ ...editData, name: e.target.value })}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Label
          </label>
          <input
            type="text"
            value={editData.label}
            onChange={(e) => setEditData({ ...editData, label: e.target.value })}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Type
          </label>
          <select
            value={editData.field_type}
            onChange={(e) => setEditData({ ...editData, field_type: e.target.value as FieldType })}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            {fieldTypes.map((type) => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>
        
        <div className="flex items-center pt-6">
          <input
            type="checkbox"
            checked={editData.required}
            onChange={(e) => setEditData({ ...editData, required: e.target.checked })}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label className="ml-2 block text-sm text-gray-900">
            Required field
          </label>
        </div>
      </div>
      
      <div className="flex justify-end space-x-2">
        <button
          onClick={onCancel}
          className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
        >
          <XMarkIcon className="h-4 w-4 mr-1" />
          Cancel
        </button>
        <button
          onClick={() => onSave(editData)}
          disabled={isSaving}
          className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
        >
          <CheckIcon className="h-4 w-4 mr-1" />
          {isSaving ? 'Saving...' : 'Save'}
        </button>
      </div>
    </div>
  );
}
