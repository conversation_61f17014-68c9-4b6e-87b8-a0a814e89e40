'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { documentsAPI, templatesAPI } from '@/lib/api';
import { Document, Template, ExtractedData, DocumentStatus } from '@/types';
import DocumentViewer from '@/components/DocumentViewer';
import FormFieldDetector from '@/components/FormFieldDetector';
import MockConfigPanel from '@/components/MockConfigPanel';
import { getFileUrl } from '@/lib/supabase';
import {
  ArrowLeftIcon,
  PlayIcon,
  DocumentTextIcon,
  EyeIcon,
  CogIcon,
  SparklesIcon,
  RectangleGroupIcon
} from '@heroicons/react/24/outline';

export default function DocumentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const documentId = params.id as string;

  const [document, setDocument] = useState<Document | null>(null);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [extractedData, setExtractedData] = useState<any>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [isDetectingFields, setIsDetectingFields] = useState(false);
  const [detectedFields, setDetectedFields] = useState<any[]>([]);
  const [showExtractedText, setShowExtractedText] = useState(false);
  const [activeTab, setActiveTab] = useState<'viewer' | 'fields' | 'extraction'>('viewer');
  const [showMockConfig, setShowMockConfig] = useState(false);
  const [documentUrl, setDocumentUrl] = useState<string>('');

  useEffect(() => {
    if (documentId) {
      fetchDocument();
      fetchTemplates();
    }
  }, [documentId]);

  useEffect(() => {
    if (document) {
      const url = getFileUrl('documents', document.file_path);
      setDocumentUrl(url);
    }
  }, [document]);

  const fetchDocument = async () => {
    try {
      const data = await documentsAPI.getById(documentId);
      setDocument(data);
    } catch (error) {
      console.error('Failed to fetch document:', error);
      router.push('/documents');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      const data = await templatesAPI.getAll();
      setTemplates(data);
    } catch (error) {
      console.error('Failed to fetch templates:', error);
    }
  };

  const handleProcessDocument = async () => {
    if (!document) return;

    setIsProcessing(true);
    try {
      await documentsAPI.process(document.id);
      await fetchDocument();
    } catch (error) {
      console.error('Failed to process document:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDetectFields = async () => {
    if (!document) return;

    setIsDetectingFields(true);
    try {
      const response = await fetch(`/api/documents/${document.id}/detect-fields`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to detect fields');
      }

      const data = await response.json();
      setDetectedFields(data.detected_fields || []);
      setActiveTab('fields');
    } catch (error) {
      console.error('Failed to detect fields:', error);
    } finally {
      setIsDetectingFields(false);
    }
  };

  const handleExtractData = async () => {
    if (!document || !selectedTemplate) return;

    setIsExtracting(true);
    try {
      const response = await fetch(`/api/documents/${document.id}/extract-data`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          template_id: selectedTemplate
        })
      });

      if (!response.ok) {
        throw new Error('Failed to extract data');
      }

      const data = await response.json();
      setExtractedData(data);
      setActiveTab('extraction');
    } catch (error) {
      console.error('Failed to extract data:', error);
    } finally {
      setIsExtracting(false);
    }
  };

  const handleTextEdit = async (newText: string) => {
    if (!document) return;

    // Update the document's extracted text
    setDocument(prev => prev ? { ...prev, extracted_text: newText } : null);

    // In a real app, you might want to save this to the backend
    console.log('Text updated:', newText);
  };

  const handleFieldsChange = (fields: any[]) => {
    setDetectedFields(fields);
  };

  const handleSaveTemplate = async (fields: any[]) => {
    try {
      const templateName = prompt('Enter template name:');
      if (!templateName) return;

      const templateData = {
        name: templateName,
        description: `Template created from ${document?.original_filename}`,
        fields: fields.map(field => ({
          name: field.name,
          label: field.label,
          field_type: field.type,
          required: field.required,
          order: fields.indexOf(field)
        }))
      };

      await templatesAPI.create(templateData);
      await fetchTemplates();
      alert('Template saved successfully!');
    } catch (error) {
      console.error('Failed to save template:', error);
      alert('Failed to save template');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case DocumentStatus.PROCESSING:
        return 'bg-yellow-100 text-yellow-800';
      case DocumentStatus.ERROR:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">Document not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The document you're looking for doesn't exist.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back
          </button>
          <h1 className="text-2xl font-semibold text-gray-900">
            {document.original_filename}
          </h1>
        </div>

        <div className="flex items-center space-x-2">
          {/* Mock Configuration Button */}
          <button
            onClick={() => setShowMockConfig(true)}
            className="p-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md"
            title="Mock Configuration"
          >
            <CogIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Document Info & Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div>
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(document.status)}`}>
                  {document.status}
                </span>
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">File Size</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {formatFileSize(document.file_size)}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Content Type</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {document.content_type}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Uploaded</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {new Date(document.created_at).toLocaleDateString()}
              </dd>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="flex flex-wrap gap-3">
              {/* Process Document */}
              {document.status === DocumentStatus.UPLOADED && (
                <button
                  onClick={handleProcessDocument}
                  disabled={isProcessing}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {isProcessing ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <SparklesIcon className="h-4 w-4 mr-2" />
                  )}
                  {isProcessing ? 'Processing...' : 'Extract Text (OCR)'}
                </button>
              )}

              {/* Detect Form Fields */}
              {document.content_type.startsWith('image/') && (
                <button
                  onClick={handleDetectFields}
                  disabled={isDetectingFields}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
                >
                  {isDetectingFields ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <RectangleGroupIcon className="h-4 w-4 mr-2" />
                  )}
                  {isDetectingFields ? 'Detecting...' : 'Detect Form Fields'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('viewer')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'viewer'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <EyeIcon className="h-4 w-4 inline mr-2" />
              Document Viewer
            </button>

            {document.content_type.startsWith('image/') && (
              <button
                onClick={() => setActiveTab('fields')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'fields'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <RectangleGroupIcon className="h-4 w-4 inline mr-2" />
                Form Fields ({detectedFields.length})
              </button>
            )}

            {document.status === DocumentStatus.COMPLETED && (
              <button
                onClick={() => setActiveTab('extraction')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'extraction'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <DocumentTextIcon className="h-4 w-4 inline mr-2" />
                Data Extraction
              </button>
            )}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'viewer' && (
            <DocumentViewer
              document={document}
              extractedText={document.extracted_text}
              onTextEdit={handleTextEdit}
              showExtractedText={showExtractedText}
              onToggleView={() => setShowExtractedText(!showExtractedText)}
            />
          )}

          {activeTab === 'fields' && document.content_type.startsWith('image/') && (
            <FormFieldDetector
              imageUrl={documentUrl}
              detectedFields={detectedFields}
              onFieldsChange={handleFieldsChange}
              onSaveTemplate={handleSaveTemplate}
              isEditable={true}
            />
          )}

          {activeTab === 'extraction' && document.status === DocumentStatus.COMPLETED && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Extract Structured Data</h3>
              </div>

              <div className="flex items-center space-x-4">
                <select
                  value={selectedTemplate}
                  onChange={(e) => setSelectedTemplate(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  <option value="">Select a template</option>
                  {templates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.name}
                    </option>
                  ))}
                </select>
                <button
                  onClick={handleExtractData}
                  disabled={!selectedTemplate || isExtracting}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                >
                  {isExtracting ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <SparklesIcon className="h-4 w-4 mr-2" />
                  )}
                  {isExtracting ? 'Extracting...' : 'Generate'}
                </button>
              </div>

              {/* Extracted Data Results */}
              {extractedData && (
                <div className="bg-gray-50 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Extracted Data</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(extractedData.extracted_data || {}).map(([key, value]: [string, any]) => (
                      <div key={key} className="bg-white rounded-md p-4 border">
                        <div className="flex items-center justify-between mb-2">
                          <label className="text-sm font-medium text-gray-700 capitalize">
                            {key.replace(/_/g, ' ')}
                          </label>
                          <span className="text-xs text-gray-500">
                            {Math.round((value?.confidence || 0) * 100)}% confidence
                          </span>
                        </div>
                        <input
                          type="text"
                          value={value?.value || ''}
                          readOnly
                          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                        />
                      </div>
                    ))}
                  </div>

                  {extractedData.overall_confidence && (
                    <div className="mt-4 text-sm text-gray-600">
                      Overall Confidence: {Math.round(extractedData.overall_confidence * 100)}%
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Error Display */}
      {document.processing_error && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-red-900 mb-4">
              Processing Error
            </h3>
            <div className="bg-red-50 rounded-md p-4">
              <p className="text-sm text-red-700">
                {document.processing_error}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Mock Configuration Panel */}
      <MockConfigPanel
        isOpen={showMockConfig}
        onClose={() => setShowMockConfig(false)}
      />
    </div>
  );
}
