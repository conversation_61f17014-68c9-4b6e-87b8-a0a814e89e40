'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { templatesAPI } from '@/lib/api';
import { FieldType } from '@/types';
import {
  ArrowLeftIcon,
  PlusIcon,
  TrashIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';

interface TemplateFieldData {
  name: string;
  label: string;
  field_type: FieldType;
  required: boolean;
  description: string;
}

export default function CreateTemplatePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [templateData, setTemplateData] = useState({
    name: '',
    description: '',
    category: '',
  });
  const [fields, setFields] = useState<TemplateFieldData[]>([]);

  const fieldTypes = Object.values(FieldType);
  const categories = ['Auto', 'Home', 'Life', 'Health', 'Business'];

  const handleAddField = () => {
    setFields([
      ...fields,
      {
        name: '',
        label: '',
        field_type: FieldType.TEXT,
        required: false,
        description: '',
      },
    ]);
  };

  const handleUpdateField = (index: number, updates: Partial<TemplateFieldData>) => {
    const updatedFields = [...fields];
    updatedFields[index] = { ...updatedFields[index], ...updates };
    setFields(updatedFields);
  };

  const handleRemoveField = (index: number) => {
    setFields(fields.filter((_, i) => i !== index));
  };

  const loadPresetTemplate = (type: 'auto' | 'home' | 'life') => {
    const presets = {
      auto: {
        name: 'Auto Insurance Template',
        description: 'Template for extracting data from auto insurance documents',
        category: 'Auto',
        fields: [
          { name: 'policy_number', label: 'Policy Number', field_type: FieldType.TEXT, required: true, description: 'Insurance policy number' },
          { name: 'policyholder_name', label: 'Policyholder Name', field_type: FieldType.TEXT, required: true, description: 'Name of the policyholder' },
          { name: 'vehicle_year', label: 'Vehicle Year', field_type: FieldType.NUMBER, required: true, description: 'Year of the vehicle' },
          { name: 'vehicle_make', label: 'Vehicle Make', field_type: FieldType.TEXT, required: true, description: 'Make of the vehicle' },
          { name: 'vehicle_model', label: 'Vehicle Model', field_type: FieldType.TEXT, required: true, description: 'Model of the vehicle' },
          { name: 'vin', label: 'VIN', field_type: FieldType.TEXT, required: true, description: 'Vehicle identification number' },
          { name: 'coverage_amount', label: 'Coverage Amount', field_type: FieldType.CURRENCY, required: true, description: 'Total coverage amount' },
          { name: 'deductible', label: 'Deductible', field_type: FieldType.CURRENCY, required: true, description: 'Insurance deductible amount' },
          { name: 'policy_start_date', label: 'Policy Start Date', field_type: FieldType.DATE, required: true, description: 'Policy effective start date' },
          { name: 'policy_end_date', label: 'Policy End Date', field_type: FieldType.DATE, required: true, description: 'Policy expiration date' }
        ]
      },
      home: {
        name: 'Home Insurance Template',
        description: 'Template for extracting data from homeowners insurance documents',
        category: 'Home',
        fields: [
          { name: 'policy_number', label: 'Policy Number', field_type: FieldType.TEXT, required: true, description: 'Insurance policy number' },
          { name: 'policyholder_name', label: 'Policyholder Name', field_type: FieldType.TEXT, required: true, description: 'Name of the policyholder' },
          { name: 'property_address', label: 'Property Address', field_type: FieldType.TEXT, required: true, description: 'Address of the insured property' },
          { name: 'dwelling_coverage', label: 'Dwelling Coverage', field_type: FieldType.CURRENCY, required: true, description: 'Coverage amount for the dwelling' },
          { name: 'personal_property', label: 'Personal Property Coverage', field_type: FieldType.CURRENCY, required: true, description: 'Coverage for personal belongings' },
          { name: 'liability_coverage', label: 'Liability Coverage', field_type: FieldType.CURRENCY, required: true, description: 'Personal liability coverage amount' },
          { name: 'deductible', label: 'Deductible', field_type: FieldType.CURRENCY, required: true, description: 'Insurance deductible amount' },
          { name: 'policy_start_date', label: 'Policy Start Date', field_type: FieldType.DATE, required: true, description: 'Policy effective start date' },
          { name: 'policy_end_date', label: 'Policy End Date', field_type: FieldType.DATE, required: true, description: 'Policy expiration date' }
        ]
      },
      life: {
        name: 'Life Insurance Template',
        description: 'Template for extracting data from life insurance documents',
        category: 'Life',
        fields: [
          { name: 'policy_number', label: 'Policy Number', field_type: FieldType.TEXT, required: true, description: 'Insurance policy number' },
          { name: 'insured_name', label: 'Insured Name', field_type: FieldType.TEXT, required: true, description: 'Name of the insured person' },
          { name: 'beneficiary_name', label: 'Beneficiary Name', field_type: FieldType.TEXT, required: true, description: 'Name of the primary beneficiary' },
          { name: 'coverage_amount', label: 'Coverage Amount', field_type: FieldType.CURRENCY, required: true, description: 'Death benefit amount' },
          { name: 'premium_amount', label: 'Premium Amount', field_type: FieldType.CURRENCY, required: true, description: 'Premium payment amount' },
          { name: 'policy_type', label: 'Policy Type', field_type: FieldType.TEXT, required: true, description: 'Type of life insurance policy' },
          { name: 'term_length', label: 'Term Length', field_type: FieldType.NUMBER, required: false, description: 'Length of term in years (for term policies)' },
          { name: 'policy_start_date', label: 'Policy Start Date', field_type: FieldType.DATE, required: true, description: 'Policy effective start date' }
        ]
      }
    };

    const preset = presets[type];
    setTemplateData({
      name: preset.name,
      description: preset.description,
      category: preset.category
    });
    setFields(preset.fields);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!templateData.name || fields.length === 0) {
      alert('Please provide a template name and at least one field');
      return;
    }

    // Validate all fields have names
    const invalidFields = fields.some(field => !field.name || !field.label);
    if (invalidFields) {
      alert('Please provide names and labels for all fields');
      return;
    }

    setIsLoading(true);
    try {
      const template = await templatesAPI.create({
        ...templateData,
        fields: fields.map(field => ({
          name: field.name,
          label: field.label,
          field_type: field.field_type,
          required: field.required,
          description: field.description,
        })),
      });

      router.push(`/templates/${template.id}`);
    } catch (error) {
      console.error('Failed to create template:', error);
      alert('Failed to create template. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => router.back()}
          className="flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back
        </button>
        <h1 className="text-2xl font-semibold text-gray-900">
          Create Template
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Template Info */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Template Information
            </h3>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Template Name *
                </label>
                <input
                  type="text"
                  required
                  value={templateData.name}
                  onChange={(e) => setTemplateData({ ...templateData, name: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="e.g., Auto Insurance Template"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  value={templateData.category}
                  onChange={(e) => setTemplateData({ ...templateData, category: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                rows={3}
                value={templateData.description}
                onChange={(e) => setTemplateData({ ...templateData, description: e.target.value })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Describe what this template is used for..."
              />
            </div>

            {/* Preset Templates */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Quick Start with Presets</h4>
              <p className="text-sm text-gray-500 mb-4">
                Load a pre-configured template with common insurance fields to get started quickly.
              </p>
              <div className="flex flex-wrap gap-3">
                <button
                  type="button"
                  onClick={() => loadPresetTemplate('auto')}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  🚗 Auto Insurance
                </button>
                <button
                  type="button"
                  onClick={() => loadPresetTemplate('home')}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  🏠 Home Insurance
                </button>
                <button
                  type="button"
                  onClick={() => loadPresetTemplate('life')}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  👤 Life Insurance
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Template Fields */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Template Fields
              </h3>
              <button
                type="button"
                onClick={handleAddField}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Add Field
              </button>
            </div>

            {fields.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-sm text-gray-500">
                  No fields added yet. Click "Add Field" to get started.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-sm font-medium text-gray-900">
                        Field {index + 1}
                      </h4>
                      <button
                        type="button"
                        onClick={() => handleRemoveField(index)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Field Name *
                        </label>
                        <input
                          type="text"
                          required
                          value={field.name}
                          onChange={(e) => handleUpdateField(index, { name: e.target.value })}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          placeholder="field_name"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Label *
                        </label>
                        <input
                          type="text"
                          required
                          value={field.label}
                          onChange={(e) => handleUpdateField(index, { label: e.target.value })}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          placeholder="Field Label"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Type
                        </label>
                        <select
                          value={field.field_type}
                          onChange={(e) => handleUpdateField(index, { field_type: e.target.value as FieldType })}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                          {fieldTypes.map((type) => (
                            <option key={type} value={type}>
                              {type}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="flex items-center pt-6">
                        <input
                          type="checkbox"
                          checked={field.required}
                          onChange={(e) => handleUpdateField(index, { required: e.target.checked })}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-900">
                          Required field
                        </label>
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700">
                        Description
                      </label>
                      <textarea
                        rows={2}
                        value={field.description}
                        onChange={(e) => handleUpdateField(index, { description: e.target.value })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Describe this field..."
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading || !templateData.name || fields.length === 0}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <CheckIcon className="h-4 w-4 mr-2" />
            )}
            {isLoading ? 'Creating...' : 'Create Template'}
          </button>
        </div>
      </form>
    </div>
  );
}
