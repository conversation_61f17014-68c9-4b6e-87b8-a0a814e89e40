'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { templatesAPI } from '@/lib/api';
import { Template, TemplateField } from '@/types';
import {
  ArrowLeftIcon,
  PencilIcon,
  StarIcon,
  RectangleStackIcon,
} from '@heroicons/react/24/outline';

export default function TemplateDetailPage() {
  const params = useParams();
  const router = useRouter();
  const templateId = params.id as string;

  const [template, setTemplate] = useState<Template | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (templateId) {
      fetchTemplate();
    }
  }, [templateId]);

  const fetchTemplate = async () => {
    try {
      const data = await templatesAPI.getById(templateId);
      setTemplate(data);
    } catch (error) {
      console.error('Failed to fetch template:', error);
      router.push('/templates');
    } finally {
      setIsLoading(false);
    }
  };

  const getFieldTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      text: 'bg-blue-100 text-blue-800',
      number: 'bg-green-100 text-green-800',
      date: 'bg-purple-100 text-purple-800',
      email: 'bg-orange-100 text-orange-800',
      phone: 'bg-pink-100 text-pink-800',
      currency: 'bg-yellow-100 text-yellow-800',
      checkbox: 'bg-indigo-100 text-indigo-800',
      radio: 'bg-red-100 text-red-800',
      select: 'bg-gray-100 text-gray-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getCategoryColor = (category: string | null) => {
    const colors: Record<string, string> = {
      'auto': 'bg-blue-100 text-blue-800',
      'home': 'bg-green-100 text-green-800',
      'life': 'bg-purple-100 text-purple-800',
      'health': 'bg-orange-100 text-orange-800',
      'business': 'bg-indigo-100 text-indigo-800',
    };
    return colors[category?.toLowerCase() || ''] || 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">Template not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The template you're looking for doesn't exist.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back
          </button>
          <div className="flex items-center space-x-3">
            <RectangleStackIcon className="h-8 w-8 text-gray-400" />
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
                {template.name}
                {template.is_system && (
                  <StarIcon className="h-5 w-5 text-yellow-400 ml-2" />
                )}
              </h1>
              {template.description && (
                <p className="text-sm text-gray-500">{template.description}</p>
              )}
            </div>
          </div>
        </div>
        {!template.is_system && (
          <button
            onClick={() => router.push(`/templates/${template.id}/edit`)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit Template
          </button>
        )}
      </div>

      {/* Template Info */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div>
              <dt className="text-sm font-medium text-gray-500">Category</dt>
              <dd className="mt-1">
                {template.category ? (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(template.category)}`}>
                    {template.category}
                  </span>
                ) : (
                  <span className="text-sm text-gray-400">No category</span>
                )}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Fields</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {template.fields.length} fields
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Type</dt>
              <dd className="mt-1">
                {template.is_system ? (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    System Template
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Custom Template
                  </span>
                )}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {new Date(template.created_at).toLocaleDateString()}
              </dd>
            </div>
          </div>
        </div>
      </div>

      {/* Template Fields */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Template Fields
          </h3>
          
          {template.fields.length === 0 ? (
            <div className="text-center py-8">
              <RectangleStackIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No fields</h3>
              <p className="mt-1 text-sm text-gray-500">
                This template doesn't have any fields defined.
              </p>
            </div>
          ) : (
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Field Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Label
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Required
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {template.fields.map((field) => (
                    <tr key={field.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {field.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {field.label || field.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getFieldTypeColor(field.field_type)}`}>
                          {field.field_type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {field.required ? (
                          <span className="text-red-600">Required</span>
                        ) : (
                          <span className="text-gray-400">Optional</span>
                        )}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {field.description || (
                          <span className="text-gray-400">No description</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          How to use this template
        </h4>
        <div className="text-sm text-blue-700 space-y-1">
          <p>1. Upload a document that matches this template type</p>
          <p>2. Process the document to extract text</p>
          <p>3. Use this template to extract structured data from the document</p>
          <p>4. Review and export the extracted data</p>
        </div>
      </div>
    </div>
  );
}
